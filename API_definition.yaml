openapi: 3.0.3
info:
  title: Vehicle Parking Management System API
  description: |
    RESTful API for managing vehicle parking operations including parking lots, 
    spots, reservations, and user management. This API provides comprehensive 
    endpoints for both admin and user operations.
  version: 1.0.0
  contact:
    name: Vehicle Parking Management System
    email: <EMAIL>

servers:
  - url: http://127.0.0.1:5000
    description: Development server

paths:
  /api/parking_lots:
    get:
      summary: Get all parking lots
      description: Retrieve a list of all parking lots with availability information
      tags:
        - Parking Lots
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  parking_lots:
                    type: array
                    items:
                      $ref: '#/components/schemas/ParkingLot'
              example:
                parking_lots:
                  - id: 1
                    location_name: "Central Mall Parking"
                    address: "123 Main Street"
                    city: "Mumbai"
                    state: "Maharashtra"
                    pin_code: "400001"
                    price_per_hour: 50.0
                    maximum_spots: 100
                    available_spots: 75
                    occupied_spots: 25

  /api/parking_lots/{lot_id}:
    get:
      summary: Get specific parking lot details
      description: Retrieve detailed information about a specific parking lot including all parking spots
      tags:
        - Parking Lots
      parameters:
        - name: lot_id
          in: path
          required: true
          description: Unique identifier of the parking lot
          schema:
            type: integer
            example: 1
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ParkingLot'
                  - type: object
                    properties:
                      parking_spots:
                        type: array
                        items:
                          $ref: '#/components/schemas/ParkingSpot'
        '404':
          description: Parking lot not found

  /api/parking_spots:
    get:
      summary: Get all parking spots
      description: Retrieve a list of all parking spots across all lots with their current status
      tags:
        - Parking Spots
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  parking_spots:
                    type: array
                    items:
                      allOf:
                        - $ref: '#/components/schemas/ParkingSpot'
                        - type: object
                          properties:
                            parking_lot:
                              type: string
                              description: Name of the parking lot
                              example: "Central Mall Parking"

  /api/reservations:
    get:
      summary: Get all reservations
      description: Retrieve a list of all parking reservations with user and spot details
      tags:
        - Reservations
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  reservations:
                    type: array
                    items:
                      $ref: '#/components/schemas/Reservation'

  /admin/chart:
    get:
      summary: Get admin revenue chart
      description: Generate and return revenue chart data as base64 encoded image
      tags:
        - Charts
      security:
        - AdminAuth: []
      responses:
        '200':
          description: Chart data returned successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  chart:
                    type: string
                    format: byte
                    description: Base64 encoded chart image
        '401':
          description: Unauthorized - Admin access required

  /user/chart/{user_id}:
    get:
      summary: Get user parking cost chart
      description: Generate user-specific parking cost chart
      tags:
        - Charts
      parameters:
        - name: user_id
          in: path
          required: true
          description: User ID for chart generation
          schema:
            type: integer
      responses:
        '200':
          description: User chart generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  chart:
                    type: string
                    format: byte
                    description: Base64 encoded chart image

components:
  schemas:
    ParkingLot:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier
          example: 1
        location_name:
          type: string
          description: Name of the parking location
          example: "Central Mall Parking"
        address:
          type: string
          description: Street address
          example: "123 Main Street"
        city:
          type: string
          description: City name
          example: "Mumbai"
        state:
          type: string
          description: State name
          example: "Maharashtra"
        pin_code:
          type: string
          description: Postal code
          example: "400001"
        price_per_hour:
          type: number
          format: float
          description: Hourly parking rate
          example: 50.0
        maximum_spots:
          type: integer
          description: Total number of parking spots
          example: 100
        available_spots:
          type: integer
          description: Currently available spots
          example: 75
        occupied_spots:
          type: integer
          description: Currently occupied spots
          example: 25

    ParkingSpot:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier
          example: 1
        lot_id:
          type: integer
          description: Associated parking lot ID
          example: 1
        spot_number:
          type: string
          description: Spot identifier
          example: "S001"
        status:
          type: string
          enum: [A, O]
          description: Spot status (A=Available, O=Occupied)
          example: "A"

    Reservation:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier
          example: 1
        spot_id:
          type: integer
          description: Reserved parking spot ID
          example: 1
        user_id:
          type: integer
          description: User who made the reservation
          example: 2
        vehicle_number:
          type: string
          description: Vehicle registration number
          example: "MH01AB1234"
        parking_timestamp:
          type: string
          format: date-time
          description: When parking started
          example: "2025-01-15T10:30:00"
        leaving_timestamp:
          type: string
          format: date-time
          description: When parking ended
          example: "2025-01-15T14:30:00"
        parking_cost:
          type: number
          format: float
          description: Total cost of parking
          example: 200.0
        status:
          type: string
          enum: [booked, parked, completed]
          description: Reservation status
          example: "completed"
        spot_number:
          type: string
          description: Parking spot number
          example: "S001"
        parking_lot:
          type: string
          description: Parking lot name
          example: "Central Mall Parking"
        username:
          type: string
          description: User who made the reservation
          example: "john_doe"

  securitySchemes:
    AdminAuth:
      type: http
      scheme: basic
      description: Admin authentication required

tags:
  - name: Parking Lots
    description: Operations related to parking lot management
  - name: Parking Spots
    description: Operations related to individual parking spots
  - name: Reservations
    description: Operations related to parking reservations
  - name: Charts
    description: Chart generation and analytics endpoints
