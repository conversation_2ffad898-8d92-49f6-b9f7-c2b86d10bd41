# Vehicle Parking Application

A comprehensive, multi-user vehicle parking management system built with Flask. Features role-based access control, real-time parking management, advanced analytics, and a modern responsive interface.


## Technology Stack

- **Backend**: Flask (Python web framework) with modular architecture
- **Database**: SQLite with SQLAlchemy ORM and automatic schema management
- **Frontend**: HTML5, CSS3, Bootstrap, Jinja2 templating
- **Charts**: Matplotlib for advanced data visualization with base64 encoding
- **API**: Flask-RESTful for comprehensive REST API endpoints
- **Validation**: Multi-layer validation (HTML5 + server-side)
- **Timezone**: pytz for Indian Standard Time (IST) support
- **Security**: Werkzeug password hashing and session management
- **UI/UX**: Responsive design with custom gradients and modern styling
- **Testing**: Comprehensive test suites for all functionality

## Project Setup

### Prerequisites
- pip (Python package installer)

### Quick Start

1. **Clone or download the project**
   ```bash
   cd Mad1_Project_Vehicle
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the application**
   - Open your browser and go to: `http://127.0.0.1:5000`
   - The database will be automatically created on first run
   - Admin user is automatically created with sample data



## Default Credentials

### Admin Account
- **Username**: `admin`
- **Password**: `admin123`
- **Features**: Full system access, parking lot management, user management, analytics

### Sample User Accounts
- **john_doe** / **password123**
- **jane_smith** / **password123**
- **mike_wilson** / **password123**
- **alice_brown** / **password123** 
- **bob_johnson** / **password123** 

## Database Schema

### Users Table (Enhanced)
- `id` (Primary Key)
- `username` (Non-unique, allows multiple users with same name)
- `email` (Unique, primary identifier)
- `password_hash` (Werkzeug hashed)
- `phone_number` (Contact information)
- `address` (Street address)
- `city` (User's city)
- `state` (User's state)
- `pincode` (6-digit postal code)
- `is_admin` (Boolean role flag)
- `created_at` (Timestamp)

### Parking Lots Table (Enhanced)
- `id` (Primary Key)
- `location_name` (Parking lot name)
- `address` (Street address only)
- `city` (Parking lot city)
- `state` (Parking lot state)
- `pin_code` (6-digit postal code)
- `price_per_hour` (Decimal pricing)
- `maximum_spots` (Capacity)
- `created_at` (Timestamp)

### Parking Spots Table
- `id` (Primary Key)
- `lot_id` (Foreign Key to ParkingLot)
- `spot_number` (Sequential numbering)
- `status` (A=Available, O=Occupied)
- `created_at` (Timestamp)

### Reservations Table (Enhanced)
- `id` (Primary Key)
- `spot_id` (Foreign Key to ParkingSpot)
- `user_id` (Foreign Key to User)
- `vehicle_number` (License plate)
- `booking_date` (Selected booking date)
- `parking_timestamp` (Check-in time)
- `leaving_timestamp` (Check-out time)
- `parking_cost` (Calculated cost)
- `status` (booked, parked, completed)
- `created_at` (Timestamp)

## API Endpoints

### Parking Lots API
- `GET /api/parking_lots` - Get all parking lots with availability and location details
- `GET /api/parking_lots/<id>` - Get specific parking lot with real-time spot counts

### Parking Spots API
- `GET /api/parking_spots` - Get all parking spots with status and lot information

### Reservations API
- `GET /api/reservations` - Get all reservations with user and spot details

### Chart APIs
- `GET /admin/chart` - Get admin revenue chart data (base64 encoded)
- `GET /user/chart/<user_id>` - Get user-specific parking cost chart
