from flask import render_template, request, redirect, url_for, session, flash, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import pytz
import matplotlib.pyplot as plt
import io
import base64
from flask_restful import Resource


import sys


try:
    app_module = sys.modules['__main__']
    app = app_module.app
    db = app_module.db
    api = app_module.api
    User = app_module.User
    ParkingLot = app_module.ParkingLot
    ParkingSpot = app_module.ParkingSpot
    Reservation = app_module.Reservation
except AttributeError:

    app_module = sys.modules.get('app') or sys.modules.get('application.models')
    if app_module:
        app = app_module.app
        db = app_module.db
        api = app_module.api
        User = app_module.User
        ParkingLot = app_module.ParkingLot
        ParkingSpot = app_module.ParkingSpot
        Reservation = app_module.Reservation
    else:
        raise ImportError("Could not find app module")

# Helper function 
def is_logged_in():
    return 'user_id' in session

def is_admin():
    if not is_logged_in():
        return False
    user = db.session.get(User, session['user_id'])
    return user and user.is_admin

def get_current_user():
    if is_logged_in():
        return db.session.get(User, session['user_id'])
    return None


@app.route('/')
def index():
    return render_template('index.html')


@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')


        if not username or not email or not password:
            flash('All fields are required!', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('Passwords do not match!', 'error')
            return render_template('register.html')

        
        if User.query.filter_by(username=username).first():
            flash('Username already exists!', 'error')
            return render_template('register.html')

        if User.query.filter_by(email=email).first():
            flash('Email already exists!', 'error')
            return render_template('register.html')

        
        new_user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password),
            is_admin=False
        )

        try:
            db.session.add(new_user)
            db.session.commit()
            flash('Registration successful! Please login.', 'success')
            return redirect(url_for('login'))
        except Exception as e:
            db.session.rollback()
            flash('Registration failed. Please try again.', 'error')
            return render_template('register.html')

    return render_template('register.html')


@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            flash('Username and password are required!', 'error')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            session['user_id'] = user.id
            session['username'] = user.username
            session['is_admin'] = user.is_admin

            if user.is_admin:
                flash(f'Welcome {user.username}!', 'success')
                return redirect(url_for('admin_dashboard'))
            else:
                flash(f'Welcome {user.username}!', 'success')
                return redirect(url_for('user_dashboard'))
        else:
            flash('Invalid username or password!', 'error')

    return render_template('login.html')


@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully!', 'success')
    return redirect(url_for('index'))


@app.route('/admin/dashboard')
def admin_dashboard():
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    
    total_lots = ParkingLot.query.count()
    total_spots = ParkingSpot.query.count()
    occupied_spots = ParkingSpot.query.filter_by(status='O').count()
    available_spots = ParkingSpot.query.filter_by(status='A').count()
    total_users = User.query.filter_by(is_admin=False).count()

    parking_lots = ParkingLot.query.all()

    return render_template('admin_dashboard.html',
                         total_lots=total_lots,
                         total_spots=total_spots,
                         occupied_spots=occupied_spots,
                         available_spots=available_spots,
                         total_users=total_users,
                         parking_lots=parking_lots)


@app.route('/user/dashboard')
def user_dashboard():
    if not is_logged_in() or is_admin():
        flash('Access denied! User login required.', 'error')
        return redirect(url_for('login'))

    user = get_current_user()
    
    reservations = Reservation.query.filter_by(user_id=user.id).order_by(Reservation.created_at.desc()).all()
    
    parking_lots = ParkingLot.query.all()

    user_chart = generate_user_chart(user.id)

    return render_template('user_dashboard.html',
                         user=user,
                         reservations=reservations,
                         parking_lots=parking_lots,
                         user_chart=user_chart)

@app.route('/admin/create_lot', methods=['GET', 'POST'])
def create_parking_lot():
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    if request.method == 'POST':
        location_name = request.form.get('location_name')
        address = request.form.get('address')
        city = request.form.get('city')
        state = request.form.get('state')
        pin_code = request.form.get('pin_code')
        price_per_hour = request.form.get('price_per_hour')
        maximum_spots = request.form.get('maximum_spots')

        
        if not all([location_name, address, city, state, pin_code, price_per_hour, maximum_spots]):
            flash('All fields are required!', 'error')
            return redirect(url_for('admin_dashboard'))

        try:
            price_per_hour = float(price_per_hour)
            maximum_spots = int(maximum_spots)

            if price_per_hour <= 0 or maximum_spots <= 0:
                flash('Price and maximum spots must be positive numbers!', 'error')
                return redirect(url_for('admin_dashboard'))

        except ValueError:
            flash('Invalid price or maximum spots value!', 'error')
            return redirect(url_for('admin_dashboard'))

        
        new_lot = ParkingLot(
            location_name=location_name,
            address=address,
            city=city,
            state=state,
            pin_code=pin_code,
            price_per_hour=price_per_hour,
            maximum_spots=maximum_spots
        )

        try:
            db.session.add(new_lot)
            db.session.commit()

            
            for i in range(1, maximum_spots + 1):
                spot = ParkingSpot(
                    lot_id=new_lot.id,
                    spot_number=f"S{i:03d}",
                    status='A'
                )
                db.session.add(spot)

            db.session.commit()
            flash(f'Parking lot "{location_name}" created successfully with {maximum_spots} spots!', 'success')

        except Exception as e:
            db.session.rollback()
            flash('Failed to create parking lot. Please try again.', 'error')

    return redirect(url_for('admin_dashboard'))


@app.route('/admin/edit_lot_page/<int:lot_id>', methods=['GET'])
def edit_parking_lot_page(lot_id):
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    lot = db.get_or_404(ParkingLot, lot_id)
    return render_template('edit_parking_lot.html', lot=lot)


@app.route('/admin/edit_lot/<int:lot_id>', methods=['POST'])
def edit_parking_lot(lot_id):
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    lot = db.get_or_404(ParkingLot, lot_id)

    location_name = request.form.get('location_name')
    address = request.form.get('address')
    city = request.form.get('city')
    state = request.form.get('state')
    pin_code = request.form.get('pin_code')
    price_per_hour = request.form.get('price_per_hour')
    maximum_spots = request.form.get('maximum_spots')

    
    if not all([location_name, address, city, state, pin_code, price_per_hour, maximum_spots]):
        flash('All fields are required!', 'error')
        return redirect(url_for('admin_dashboard'))

    try:
        price_per_hour = float(price_per_hour)
        maximum_spots = int(maximum_spots)

        if price_per_hour <= 0 or maximum_spots <= 0:
            flash('Price and maximum spots must be positive numbers!', 'error')
            return redirect(url_for('admin_dashboard'))

    except ValueError:
        flash('Invalid price or maximum spots value!', 'error')
        return redirect(url_for('admin_dashboard'))

    try:
        lot.location_name = location_name
        lot.address = address
        lot.city = city
        lot.state = state
        lot.pin_code = pin_code
        lot.price_per_hour = price_per_hour

        current_spots = len(lot.parking_spots)

        if maximum_spots > current_spots:
            for i in range(current_spots + 1, maximum_spots + 1):
                spot = ParkingSpot(
                    lot_id=lot.id,
                    spot_number=f"S{i:03d}",
                    status='A'
                )
                db.session.add(spot)
        elif maximum_spots < current_spots:
            spots_to_remove = ParkingSpot.query.filter_by(lot_id=lot.id, status='A').order_by(ParkingSpot.id.desc()).limit(current_spots - maximum_spots).all()

            if len(spots_to_remove) < (current_spots - maximum_spots):
                flash('Cannot reduce spots: Some spots are currently occupied!', 'error')
                return redirect(url_for('admin_dashboard'))

            for spot in spots_to_remove:
                db.session.delete(spot)

        lot.maximum_spots = maximum_spots
        db.session.commit()
        flash(f'Parking lot "{location_name}" updated successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to update parking lot. Please try again.', 'error')

    return redirect(url_for('admin_dashboard'))

@app.route('/admin/delete_lot_page/<int:lot_id>', methods=['GET'])
def delete_parking_lot_page(lot_id):
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    lot = db.get_or_404(ParkingLot, lot_id)
    return render_template('delete_parking_lot.html', lot=lot)

@app.route('/admin/delete_lot/<int:lot_id>', methods=['POST'])
def delete_parking_lot(lot_id):
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    lot = db.get_or_404(ParkingLot, lot_id)

    occupied_spots = ParkingSpot.query.filter_by(lot_id=lot.id, status='O').count()

    if occupied_spots > 0:
        flash(f'Cannot delete parking lot: {occupied_spots} spots are currently occupied!', 'error')
        return redirect(url_for('admin_dashboard'))

    try:
        ParkingSpot.query.filter_by(lot_id=lot.id).delete()

        db.session.delete(lot)
        db.session.commit()

        flash(f'Parking lot "{lot.location_name}" deleted successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to delete parking lot. Please try again.', 'error')

    return redirect(url_for('admin_dashboard'))

@app.route('/user/book_spot', methods=['POST'])
def book_parking_spot():
    if not is_logged_in() or is_admin():
        flash('Access denied! User login required.', 'error')
        return redirect(url_for('login'))

    user = get_current_user()
    lot_id = request.form.get('lot_id')
    vehicle_number = request.form.get('vehicle_number')
    booking_date = request.form.get('booking_date')

    if not lot_id or not vehicle_number or not booking_date:
        flash('Parking lot, vehicle number, and booking date are required!', 'error')
        return redirect(url_for('user_dashboard'))

    try:
        lot_id = int(lot_id)
    except ValueError:
        flash('Invalid parking lot selected!', 'error')
        return redirect(url_for('user_dashboard'))

    try:
        from datetime import datetime, date, timedelta
        booking_date_obj = datetime.strptime(booking_date, '%Y-%m-%d').date()
        today = date.today()
        max_date = date.today() + timedelta(days=30)

        if booking_date_obj < today:
            flash('Cannot book parking for past dates!', 'error')
            return redirect(url_for('user_dashboard'))

        if booking_date_obj > max_date:
            flash('Cannot book parking more than 30 days in advance!', 'error')
            return redirect(url_for('user_dashboard'))

    except ValueError:
        flash('Invalid booking date format!', 'error')
        return redirect(url_for('user_dashboard'))

    active_reservation = Reservation.query.filter(
        Reservation.user_id == user.id,
        Reservation.status.in_(['booked', 'parked'])
    ).first()

    if active_reservation:
        flash('You already have an active reservation!', 'error')
        return redirect(url_for('user_dashboard'))

    available_spot = ParkingSpot.query.filter_by(lot_id=lot_id, status='A').first()

    if not available_spot:
        flash('No available spots in this parking lot!', 'error')
        return redirect(url_for('user_dashboard'))

    try:
        reservation = Reservation(
            spot_id=available_spot.id,
            user_id=user.id,
            vehicle_number=vehicle_number.upper(),
            status='booked'
        )

        available_spot.status = 'O'

        db.session.add(reservation)
        db.session.commit()

        flash(f'Parking spot {available_spot.spot_number} booked successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to book parking spot. Please try again.', 'error')

    return redirect(url_for('user_dashboard'))

# User - Park Vehicle (Check-in)
@app.route('/user/park_vehicle/<int:reservation_id>', methods=['POST'])
def park_vehicle(reservation_id):
    if not is_logged_in() or is_admin():
        flash('Access denied! User login required.', 'error')
        return redirect(url_for('login'))

    user = get_current_user()
    reservation = Reservation.query.filter_by(id=reservation_id, user_id=user.id).first()

    if not reservation:
        flash('Reservation not found!', 'error')
        return redirect(url_for('user_dashboard'))

    if reservation.status != 'booked':
        flash('Invalid reservation status!', 'error')
        return redirect(url_for('user_dashboard'))

    try:
        # Set parking timestamp (IST)
        ist = pytz.timezone('Asia/Kolkata')
        reservation.parking_timestamp = datetime.now(ist)
        reservation.status = 'parked'

        db.session.commit()
        flash('Vehicle parked successfully! Parking timer started.', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to park vehicle. Please try again.', 'error')

    return redirect(url_for('user_dashboard'))

# User - Release Parking Spot (Check-out)
@app.route('/user/release_spot/<int:reservation_id>', methods=['POST'])
def release_parking_spot(reservation_id):
    if not is_logged_in() or is_admin():
        flash('Access denied! User login required.', 'error')
        return redirect(url_for('login'))

    user = get_current_user()
    reservation = Reservation.query.filter_by(id=reservation_id, user_id=user.id).first()

    if not reservation:
        flash('Reservation not found!', 'error')
        return redirect(url_for('user_dashboard'))

    if reservation.status not in ['booked', 'parked']:
        flash('Invalid reservation status!', 'error')
        return redirect(url_for('user_dashboard'))

    try:
        # Set leaving timestamp (IST)
        ist = pytz.timezone('Asia/Kolkata')
        reservation.leaving_timestamp = datetime.now(ist)

        # Calculate cost if vehicle was parked
        if reservation.status == 'parked' and reservation.parking_timestamp:
            reservation.parking_cost = reservation.calculate_cost()

        reservation.status = 'completed'

        # Mark spot as available
        parking_spot = db.session.get(ParkingSpot, reservation.spot_id)
        parking_spot.status = 'A'

        db.session.commit()

        if reservation.parking_cost > 0:
            flash(f'Parking spot released successfully! Total cost: ₹{reservation.parking_cost}', 'success')
        else:
            flash('Parking spot released successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to release parking spot. Please try again.', 'error')

    return redirect(url_for('user_dashboard'))

# Admin - View All Users
@app.route('/admin/users')
def view_users():
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    users = User.query.filter_by(is_admin=False).order_by(User.created_at.desc()).all()
    return render_template('admin_users.html', users=users)

# Admin - Search Parking Spot
@app.route('/admin/search_spot', methods=['GET', 'POST'])
def search_parking_spot():
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    spot = None
    reservation = None

    if request.method == 'POST':
        search_query = request.form.get('search_query')

        if search_query:
            # Search by spot number or vehicle number
            spot = ParkingSpot.query.filter(
                ParkingSpot.spot_number.ilike(f'%{search_query}%')
            ).first()

            if not spot:
                # Search by vehicle number in reservations
                reservation = Reservation.query.filter(
                    Reservation.vehicle_number.ilike(f'%{search_query}%'),
                    Reservation.status.in_(['booked', 'parked'])
                ).first()

                if reservation:
                    spot = reservation.parking_spot

            if not spot:
                flash('No parking spot found with the given search criteria!', 'error')
        else:
            flash('Please enter a search query!', 'error')

    return render_template('admin_search.html', spot=spot, reservation=reservation)

# Generate chart for admin dashboard
@app.route('/admin/chart')
def admin_chart():
    if not is_admin():
        return jsonify({'error': 'Access denied'}), 403

    try:
        # Get data for charts
        parking_lots = ParkingLot.query.all()

        if not parking_lots:
            return jsonify({'chart': None})

        lot_names = [lot.location_name for lot in parking_lots]
        available_counts = [lot.get_available_spots_count() for lot in parking_lots]
        occupied_counts = [lot.get_occupied_spots_count() for lot in parking_lots]

        # Create chart
        plt.figure(figsize=(12, 6))

        # Bar chart for parking lot occupancy
        x = range(len(lot_names))
        width = 0.35

        plt.bar([i - width/2 for i in x], available_counts, width, label='Available', color='#10b981', alpha=0.8)
        plt.bar([i + width/2 for i in x], occupied_counts, width, label='Occupied', color='#ef4444', alpha=0.8)

        plt.xlabel('Parking Lots')
        plt.ylabel('Number of Spots')
        plt.title('Parking Lot Occupancy Status')
        plt.xticks(x, [name[:15] + '...' if len(name) > 15 else name for name in lot_names], rotation=45, ha='right')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # Convert plot to base64 string
        img = io.BytesIO()
        plt.savefig(img, format='png', dpi=150, bbox_inches='tight')
        img.seek(0)
        chart_url = base64.b64encode(img.getvalue()).decode()
        plt.close()

        return jsonify({'chart': chart_url})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Generate chart for user dashboard
@app.route('/user/chart')
def user_chart():
    if not is_logged_in() or is_admin():
        return jsonify({'error': 'Access denied'}), 403

    user = get_current_user()

    # Get user's completed reservations
    reservations = Reservation.query.filter_by(
        user_id=user.id,
        status='completed'
    ).order_by(Reservation.created_at.desc()).limit(10).all()

    if not reservations:
        return jsonify({'chart': None, 'message': 'No parking history available'})

    # Prepare data for chart
    dates = []
    costs = []

    for reservation in reversed(reservations):
        if reservation.leaving_timestamp:
            formatted_date = reservation.leaving_timestamp.strftime('%d/%m')
            dates.append(formatted_date)
            costs.append(float(reservation.parking_cost))

    plt.figure(figsize=(10, 6))
    bars = plt.bar(dates, costs, color='#10b981', alpha=0.8, edgecolor='white', linewidth=1)
    
    for bar, cost in zip(bars, costs):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + max(costs)*0.01,
                f'₹{int(cost)}', ha='center', va='bottom', fontsize=10, fontweight='bold', color='#1f2937')

    plt.title('Your Parking Cost History', fontsize=16, fontweight='bold', color='#1f2937')
    plt.xlabel('Date', fontsize=12, fontweight='bold', color='#374151')
    plt.ylabel('Parking Cost (₹)', fontsize=12, fontweight='bold', color='#374151')
    plt.xticks(rotation=45, color='#6b7280')
    plt.yticks(color='#6b7280')
    plt.grid(True, alpha=0.3, axis='y', linestyle='--', color='#d1d5db')

    plt.gca().set_facecolor('#f9fafb')
    plt.gcf().patch.set_facecolor('white')

    plt.tight_layout()

    img = io.BytesIO()
    plt.savefig(img, format='png', dpi=150, bbox_inches='tight', facecolor='white')
    img.seek(0)
    chart_url = base64.b64encode(img.getvalue()).decode()
    plt.close()

    return jsonify({'chart': chart_url})

class ParkingLotListAPI(Resource):
    def get(self):
        """Get all parking lots"""
        lots = ParkingLot.query.all()
        result = []
        for lot in lots:
            result.append({
                'id': lot.id,
                'location_name': lot.location_name,
                'address': lot.address,
                'city': lot.city or 'Unknown',
                'state': lot.state or 'Unknown',
                'pin_code': lot.pin_code,
                'price_per_hour': lot.price_per_hour,
                'maximum_spots': lot.maximum_spots,
                'available_spots': lot.get_available_spots_count(),
                'occupied_spots': lot.get_occupied_spots_count()
            })
        return {'parking_lots': result}

class ParkingLotAPI(Resource):
    def get(self, lot_id):
        """Get specific parking lot details"""
        lot = ParkingLot.query.get_or_404(lot_id)
        return {
            'id': lot.id,
            'location_name': lot.location_name,
            'address': lot.address,
            'city': lot.city or 'Unknown',
            'state': lot.state or 'Unknown',
            'pin_code': lot.pin_code,
            'price_per_hour': lot.price_per_hour,
            'maximum_spots': lot.maximum_spots,
            'available_spots': lot.get_available_spots_count(),
            'occupied_spots': lot.get_occupied_spots_count(),
            'parking_spots': [
                {
                    'id': spot.id,
                    'spot_number': spot.spot_number,
                    'status': spot.status
                } for spot in lot.parking_spots
            ]
        }

class ParkingSpotListAPI(Resource):
    def get(self):
        """Get all parking spots"""
        spots = ParkingSpot.query.all()
        result = []
        for spot in spots:
            result.append({
                'id': spot.id,
                'lot_id': spot.lot_id,
                'spot_number': spot.spot_number,
                'status': spot.status,
                'parking_lot': spot.parking_lot.location_name
            })
        return {'parking_spots': result}

class ReservationListAPI(Resource):
    def get(self):
        """Get all reservations"""
        reservations = Reservation.query.all()
        result = []
        for reservation in reservations:
            result.append({
                'id': reservation.id,
                'spot_id': reservation.spot_id,
                'user_id': reservation.user_id,
                'vehicle_number': reservation.vehicle_number,
                'parking_timestamp': reservation.parking_timestamp.isoformat() if reservation.parking_timestamp else None,
                'leaving_timestamp': reservation.leaving_timestamp.isoformat() if reservation.leaving_timestamp else None,
                'parking_cost': reservation.parking_cost,
                'status': reservation.status,
                'spot_number': reservation.parking_spot.spot_number,
                'parking_lot': reservation.parking_spot.parking_lot.location_name,
                'username': reservation.user.username
            })
        return {'reservations': result}

@app.route('/admin/reports')
def admin_reports():
    if not is_admin():
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))

    total_revenue = sum([r.parking_cost for r in Reservation.query.filter_by(status='completed').all()])
    total_bookings = Reservation.query.count()
    active_users = User.query.filter_by(is_admin=False).count()

    total_spots = ParkingSpot.query.count()
    occupied_spots = ParkingSpot.query.filter_by(status='O').count()
    occupancy_rate = (occupied_spots / total_spots * 100) if total_spots > 0 else 0

    from sqlalchemy import func
    top_lots_query = db.session.query(
        ParkingLot,
        func.count(Reservation.id).label('booking_count'),
        func.sum(Reservation.parking_cost).label('revenue')
    ).select_from(ParkingLot).join(ParkingSpot, ParkingLot.id == ParkingSpot.lot_id).join(
        Reservation, ParkingSpot.id == Reservation.spot_id
    ).filter(
        Reservation.status == 'completed'
    ).group_by(ParkingLot.id).order_by(
        func.sum(Reservation.parking_cost).desc()
    ).limit(5).all()

    top_lots = []
    for lot, booking_count, revenue in top_lots_query:
        occupied = ParkingSpot.query.filter_by(lot_id=lot.id, status='O').count()
        lot_occupancy_rate = (occupied / lot.maximum_spots * 100) if lot.maximum_spots > 0 else 0

        top_lots.append({
            'location_name': lot.location_name,
            'address': lot.address,
            'booking_count': booking_count or 0,
            'revenue': revenue or 0,
            'avg_duration': 2.5,  
            'occupancy_rate': lot_occupancy_rate
        })

    recent_activities = db.session.query(
        Reservation, User.username, ParkingLot.location_name
    ).select_from(Reservation).join(User, Reservation.user_id == User.id).join(
        ParkingSpot, Reservation.spot_id == ParkingSpot.id
    ).join(ParkingLot, ParkingSpot.lot_id == ParkingLot.id).order_by(
        Reservation.created_at.desc()
    ).limit(10).all()

    activities = []
    for reservation, username, location_name in recent_activities:
        activities.append({
            'timestamp': reservation.created_at,
            'username': username,
            'vehicle_number': reservation.vehicle_number,
            'location_name': location_name,
            'status': reservation.status,
            'parking_cost': reservation.parking_cost
        })

    revenue_chart = generate_revenue_chart()

    return render_template('admin_reports.html',
                         total_revenue=total_revenue,
                         total_bookings=total_bookings,
                         active_users=active_users,
                         occupancy_rate=occupancy_rate,
                         top_lots=top_lots,
                         recent_activities=activities,
                         revenue_chart=revenue_chart)

def generate_revenue_chart():
    try:
        from datetime import timedelta
        from collections import defaultdict

        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        reservations = Reservation.query.filter(
            Reservation.status == 'completed',
            Reservation.leaving_timestamp >= start_date,
            Reservation.leaving_timestamp <= end_date
        ).all()

        if not reservations:
            return None

        daily_revenue = defaultdict(float)
        for reservation in reservations:
            if reservation.leaving_timestamp:
                date = reservation.leaving_timestamp.date()
                daily_revenue[date] += reservation.parking_cost

        dates = []
        revenues = []
        current_date = start_date.date()

        for i in range(7):
            dates.append(current_date.strftime('%m/%d'))
            revenues.append(daily_revenue.get(current_date, 0))
            current_date += timedelta(days=1)

        plt.figure(figsize=(10, 6))
        plt.fill_between(dates, revenues, alpha=0.3, color='#10b981')
        plt.plot(dates, revenues, color='#10b981', linewidth=2, marker='o')
        plt.title('Daily Revenue (Last 7 Days)', fontsize=16, fontweight='bold')
        plt.xlabel('Date')
        plt.ylabel('Revenue (₹)')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return chart_data
    except Exception as e:
        return None

def generate_user_chart(user_id):
    try:
        reservations = Reservation.query.filter_by(
            user_id=user_id,
            status='completed'
        ).order_by(Reservation.created_at.desc()).limit(10).all()

        if not reservations:
            return None
        dates = []
        costs = []

        for reservation in reversed(reservations):
            if reservation.leaving_timestamp:
                formatted_date = reservation.leaving_timestamp.strftime('%d/%m')
                dates.append(formatted_date)
                costs.append(float(reservation.parking_cost))

        if not dates:
            return None

        plt.figure(figsize=(10, 6))

        bars = plt.bar(dates, costs, color='#10b981', alpha=0.8, edgecolor='white', linewidth=1)
        for bar, cost in zip(bars, costs):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + max(costs)*0.01,
                    f'₹{int(cost)}', ha='center', va='bottom', fontsize=10, fontweight='bold', color='#1f2937')

        plt.title('Parking Cost History', fontsize=16, fontweight='bold', color='#1f2937')
        plt.xlabel('Date', fontsize=12, fontweight='bold', color='#374151')
        plt.ylabel('Parking Cost (₹)', fontsize=12, fontweight='bold', color='#374151')
        plt.xticks(rotation=45, color='#6b7280')
        plt.yticks(color='#6b7280')
        plt.grid(True, alpha=0.3, axis='y', linestyle='--', color='#d1d5db')

        plt.gca().set_facecolor('#f9fafb')
        plt.gcf().patch.set_facecolor('white')

        plt.tight_layout()
        plt.subplots_adjust(bottom=0.15)

        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight', facecolor='white')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode()
        plt.close()

        return chart_data
    except Exception as e:
        return None

@app.route('/profile')
def view_profile():
    if not is_logged_in():
        flash('Please login to view the profile.', 'error')
        return redirect(url_for('login'))

    user = get_current_user()
    return render_template('profile.html', user=user)

@app.route('/profile/edit', methods=['GET', 'POST'])
def edit_profile():
    if not is_logged_in():
        flash('Please login to edit the profile.', 'error')
        return redirect(url_for('login'))

    user = get_current_user()

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')

        if not username:
            flash('Username is required!', 'error')
            return render_template('edit_profile.html', user=user)

        if not email:
            flash('Email is required!', 'error')
            return render_template('edit_profile.html', user=user)

        existing_user = User.query.filter(User.username == username, User.id != user.id).first()
        if existing_user:
            flash('Username is already taken!', 'error')
            return render_template('edit_profile.html', user=user)

        existing_email = User.query.filter(User.email == email, User.id != user.id).first()
        if existing_email:
            flash('Email is already taken!', 'error')
            return render_template('edit_profile.html', user=user)

        if new_password:
            if not current_password:
                flash('Current password is required to change password!', 'error')
                return render_template('edit_profile.html', user=user)

            if not check_password_hash(user.password_hash, current_password):
                flash('Current password is incorrect!', 'error')
                return render_template('edit_profile.html', user=user)

            if new_password != confirm_password:
                flash('New passwords does not match!', 'error')
                return render_template('edit_profile.html', user=user)

            if len(new_password) < 6:
                flash('New password must be atleast 6 characters long!', 'error')
                return render_template('edit_profile.html', user=user)

        try:
            user.username = username
            user.email = email

            if new_password:
                user.password_hash = generate_password_hash(new_password)

            db.session.commit()

            session['username'] = username

            flash('Profile updated successfully!', 'success')
            return redirect(url_for('view_profile'))

        except Exception as e:
            db.session.rollback()
            flash('Failed to update the profile. Please try again.', 'error')
            return render_template('edit_profile.html', user=user)

    return render_template('edit_profile.html', user=user)


api.add_resource(ParkingLotListAPI, '/api/parking_lots')
api.add_resource(ParkingLotAPI, '/api/parking_lots/<int:lot_id>')
api.add_resource(ParkingSpotListAPI, '/api/parking_spots')
api.add_resource(ReservationListAPI, '/api/reservations')