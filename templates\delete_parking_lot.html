{% extends "base.html" %}

{% block title %}Delete Parking Lot{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-trash"></i> Delete Parking Lot
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning!</strong> This action cannot be undone.
                    </div>

                    <h5>Parking Lot Details:</h5>
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-title">{{ lot.location_name }}</h6>
                            <p class="card-text">
                                <strong>Address:</strong> {{ lot.address }}<br>
                                <strong>City:</strong> {{ lot.city }}<br>
                                <strong>State:</strong> {{ lot.state }}<br>
                                <strong>Pin Code:</strong> {{ lot.pin_code }}<br>
                                <strong>Price per Hour:</strong> ₹{{ lot.price_per_hour }}<br>
                                <strong>Maximum Spots:</strong> {{ lot.maximum_spots }}
                            </p>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> Current Status:</h6>
                        <ul class="mb-0">
                            <li>Available Spots: <span class="badge bg-success">{{ lot.get_available_spots_count() }}</span></li>
                            <li>Occupied Spots: <span class="badge bg-danger">{{ lot.get_occupied_spots_count() }}</span></li>
                            <li>Total Spots: <span class="badge bg-primary">{{ lot.maximum_spots }}</span></li>
                        </ul>
                    </div>

                    {% if lot.get_occupied_spots_count() > 0 %}
                    <div class="alert alert-danger">
                        <i class="bi bi-x-circle"></i>
                        <strong>Cannot Delete!</strong> This parking lot has {{ lot.get_occupied_spots_count() }} occupied spots. 
                        Please wait for all vehicles to leave before deleting this parking lot.
                    </div>
                    {% endif %}

                    <p class="text-danger">
                        <strong>Are you sure you want to delete the parking lot "{{ lot.location_name }}"?</strong>
                    </p>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="/admin/dashboard" class="btn btn-secondary me-md-2">
                            <i class="bi bi-arrow-left"></i> Cancel
                        </a>
                        {% if lot.get_occupied_spots_count() == 0 %}
                        <form method="POST" action="/admin/delete_lot/{{ lot.id }}" class="d-inline">
                            <button type="submit" class="btn btn-danger" 
                                    onclick="return confirm('Are you absolutely sure? This action cannot be undone!')">
                                <i class="bi bi-trash"></i> Yes, Delete Parking Lot
                            </button>
                        </form>
                        {% else %}
                        <button type="button" class="btn btn-danger" disabled>
                            <i class="bi bi-trash"></i> Cannot Delete (Occupied Spots)
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function confirmDelete() {
    return confirm('Are you absolutely sure you want to delete this parking lot? This action cannot be undone!');
}
</script>
{% endblock %}
