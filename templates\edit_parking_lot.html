{% extends "base.html" %}

{% block title %}Edit Parking Lot{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-pencil"></i> Edit Parking Lot
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="/admin/edit_lot/{{ lot.id }}" novalidate>
                        <div class="mb-3">
                            <label for="location_name" class="form-label">Location Name</label>
                            <input type="text" class="form-control" id="location_name" name="location_name" 
                                   value="{{ lot.location_name }}" required>
                            <div class="invalid-feedback">
                                Please provide a valid location name.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" required>{{ lot.address }}</textarea>
                            <div class="invalid-feedback">
                                Please provide a valid address.
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="city" class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="city" 
                                           value="{{ lot.city }}" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid city.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="state" class="form-label">State</label>
                                    <input type="text" class="form-control" id="state" name="state" 
                                           value="{{ lot.state }}" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid state.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pin_code" class="form-label">Pin Code</label>
                                    <input type="text" class="form-control" id="pin_code" name="pin_code" 
                                           value="{{ lot.pin_code }}" pattern="[0-9]{6}" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid 6-digit pin code.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price_per_hour" class="form-label">Price per Hour (₹)</label>
                                    <input type="number" class="form-control" id="price_per_hour" name="price_per_hour" 
                                           value="{{ lot.price_per_hour }}" step="0.01" min="0" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid price.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="maximum_spots" class="form-label">Maximum Spots</label>
                            <input type="number" class="form-control" id="maximum_spots" name="maximum_spots" 
                                   value="{{ lot.maximum_spots }}" min="1" required>
                            <div class="form-text">Current: {{ lot.maximum_spots }} spots</div>
                            <div class="invalid-feedback">
                                Please provide a valid number of spots (minimum 1).
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Current Status:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Available Spots: <span class="badge bg-success">{{ lot.get_available_spots_count() }}</span></li>
                                <li>Occupied Spots: <span class="badge bg-danger">{{ lot.get_occupied_spots_count() }}</span></li>
                                <li>Total Spots: <span class="badge bg-primary">{{ lot.maximum_spots }}</span></li>
                            </ul>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="/admin/dashboard" class="btn btn-secondary me-md-2">
                                <i class="bi bi-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg"></i> Update Parking Lot
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>

(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByTagName('form');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
