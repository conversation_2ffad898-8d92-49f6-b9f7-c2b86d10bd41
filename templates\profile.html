{% extends "base.html" %}

{% block title %}Profile{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-circle"></i> My Profile
                    </h1>
                </div>
                <div>
                    <a href="{{ url_for('edit_profile') }}" class="btn btn-primary">
                        <i class="bi bi-pencil-square me-2"></i>Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Information -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold text-muted">Username</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-person me-2 text-primary"></i>{{ user.username }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold text-muted">Email Address</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-envelope me-2 text-primary"></i>{{ user.email }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold text-muted">Phone Number</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-telephone me-2 text-primary"></i>{{ user.phone_number }}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold text-muted">Account Type</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                {% if user.is_admin %}
                                    <i class="bi bi-shield-check me-2 text-warning"></i>
                                    <span class="badge bg-warning text-dark">Administrator</span>
                                {% else %}
                                    <i class="bi bi-person me-2 text-success"></i>
                                    <span class="badge bg-success">Regular User</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold text-muted">Member Since</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-calendar me-2 text-primary"></i>{{ user.created_at.strftime('%B %d, %Y') if user.created_at else 'N/A' }}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold text-muted">Address</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-house me-2 text-primary"></i>{{ user.address if user.address else 'N/A' }}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold text-muted">City</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-building me-2 text-primary"></i>{{ user.city if user.city else 'N/A' }}
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold text-muted">State</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-geo-alt me-2 text-primary"></i>{{ user.state if user.state else 'N/A' }}
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold text-muted">Pincode</label>
                            <div class="form-control-plaintext bg-light rounded p-2">
                                <i class="bi bi-mailbox me-2 text-primary"></i>{{ user.pincode if user.pincode else 'N/A' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Statistics for users -->
            {% if not user.is_admin %}
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up me-2"></i>Parking Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ user.reservations|length }}</h4>
                                <small class="text-muted">Total Bookings</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border-end">
                                {% set completed_reservations = user.reservations|selectattr('status', 'equalto', 'completed')|list %}
                                <h4 class="text-success mb-1">{{ completed_reservations|length }}</h4>
                                <small class="text-muted">Completed Trips</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            {% set total_spent = completed_reservations|sum(attribute='parking_cost') %}
                            <h4 class="text-info mb-1">₹{{ "%.2f"|format(total_spent) }}</h4>
                            <small class="text-muted">Total Spent</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    {% if user.reservations %}
                        <div class="list-group list-group-flush">
                            {% for reservation in user.reservations[:5] %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">
                                        <i class="bi bi-car-front me-2"></i>{{ reservation.vehicle_number }}
                                    </h6>
                                    <p class="mb-1 text-muted">
                                        <small>{{ reservation.created_at.strftime('%B %d, %Y at %I:%M %p') }}</small>
                                    </p>
                                </div>
                                <div class="text-end">
                                    {% if reservation.status == 'completed' %}
                                        <span class="badge bg-success">Completed</span>
                                        <br><small class="text-muted">₹{{ "%.2f"|format(reservation.parking_cost) }}</small>
                                    {% elif reservation.status == 'parked' %}
                                        <span class="badge bg-warning">Parked</span>
                                    {% else %}
                                        <span class="badge bg-info">Booked</span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if user.reservations|length > 5 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('user_dashboard') if not user.is_admin else url_for('admin_dashboard') }}" class="btn btn-outline-primary btn-sm">
                                View All Activity
                            </a>
                        </div>
                        {% endif %}
                    {% else %} 
                    <div class="text-center py-4">
                            <i class="bi bi-clock-history display-4 text-muted"></i>
                            <p class="text-muted mt-2">No activity yet</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
